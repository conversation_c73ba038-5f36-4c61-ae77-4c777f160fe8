"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tippy.js";
exports.ids = ["vendor-chunks/tippy.js"];
exports.modules = {

/***/ "(ssr)/./node_modules/tippy.js/dist/tippy.esm.js":
/*!*************************************************!*\
  !*** ./node_modules/tippy.js/dist/tippy.esm.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateFill: () => (/* binding */ animateFill),\n/* harmony export */   createSingleton: () => (/* binding */ createSingleton),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   delegate: () => (/* binding */ delegate),\n/* harmony export */   followCursor: () => (/* binding */ followCursor),\n/* harmony export */   hideAll: () => (/* binding */ hideAll),\n/* harmony export */   inlinePositioning: () => (/* binding */ inlinePositioning),\n/* harmony export */   roundArrow: () => (/* binding */ ROUND_ARROW),\n/* harmony export */   sticky: () => (/* binding */ sticky)\n/* harmony export */ });\n/* harmony import */ var _popperjs_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @popperjs/core */ \"(ssr)/./node_modules/@popperjs/core/lib/popper.js\");\n/* harmony import */ var _popperjs_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @popperjs/core */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/applyStyles.js\");\n/**!\n* tippy.js v6.3.7\n* (c) 2017-2021 atomiks\n* MIT License\n*/\n\n\nvar ROUND_ARROW = '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\nvar BOX_CLASS = \"tippy-box\";\nvar CONTENT_CLASS = \"tippy-content\";\nvar BACKDROP_CLASS = \"tippy-backdrop\";\nvar ARROW_CLASS = \"tippy-arrow\";\nvar SVG_ARROW_CLASS = \"tippy-svg-arrow\";\nvar TOUCH_OPTIONS = {\n  passive: true,\n  capture: true\n};\nvar TIPPY_DEFAULT_APPEND_TO = function TIPPY_DEFAULT_APPEND_TO() {\n  return document.body;\n};\n\nfunction hasOwnProperty(obj, key) {\n  return {}.hasOwnProperty.call(obj, key);\n}\nfunction getValueAtIndexOrReturn(value, index, defaultValue) {\n  if (Array.isArray(value)) {\n    var v = value[index];\n    return v == null ? Array.isArray(defaultValue) ? defaultValue[index] : defaultValue : v;\n  }\n\n  return value;\n}\nfunction isType(value, type) {\n  var str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(type + \"]\") > -1;\n}\nfunction invokeWithArgsOrReturn(value, args) {\n  return typeof value === 'function' ? value.apply(void 0, args) : value;\n}\nfunction debounce(fn, ms) {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  var timeout;\n  return function (arg) {\n    clearTimeout(timeout);\n    timeout = setTimeout(function () {\n      fn(arg);\n    }, ms);\n  };\n}\nfunction removeProperties(obj, keys) {\n  var clone = Object.assign({}, obj);\n  keys.forEach(function (key) {\n    delete clone[key];\n  });\n  return clone;\n}\nfunction splitBySpaces(value) {\n  return value.split(/\\s+/).filter(Boolean);\n}\nfunction normalizeToArray(value) {\n  return [].concat(value);\n}\nfunction pushIfUnique(arr, value) {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\nfunction unique(arr) {\n  return arr.filter(function (item, index) {\n    return arr.indexOf(item) === index;\n  });\n}\nfunction getBasePlacement(placement) {\n  return placement.split('-')[0];\n}\nfunction arrayFrom(value) {\n  return [].slice.call(value);\n}\nfunction removeUndefinedProps(obj) {\n  return Object.keys(obj).reduce(function (acc, key) {\n    if (obj[key] !== undefined) {\n      acc[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n\nfunction div() {\n  return document.createElement('div');\n}\nfunction isElement(value) {\n  return ['Element', 'Fragment'].some(function (type) {\n    return isType(value, type);\n  });\n}\nfunction isNodeList(value) {\n  return isType(value, 'NodeList');\n}\nfunction isMouseEvent(value) {\n  return isType(value, 'MouseEvent');\n}\nfunction isReferenceElement(value) {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\nfunction getArrayOfElements(value) {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\nfunction setTransitionDuration(els, value) {\n  els.forEach(function (el) {\n    if (el) {\n      el.style.transitionDuration = value + \"ms\";\n    }\n  });\n}\nfunction setVisibilityState(els, state) {\n  els.forEach(function (el) {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\nfunction getOwnerDocument(elementOrElements) {\n  var _element$ownerDocumen;\n\n  var _normalizeToArray = normalizeToArray(elementOrElements),\n      element = _normalizeToArray[0]; // Elements created via a <template> have an ownerDocument with no reference to the body\n\n\n  return element != null && (_element$ownerDocumen = element.ownerDocument) != null && _element$ownerDocumen.body ? element.ownerDocument : document;\n}\nfunction isCursorOutsideInteractiveBorder(popperTreeData, event) {\n  var clientX = event.clientX,\n      clientY = event.clientY;\n  return popperTreeData.every(function (_ref) {\n    var popperRect = _ref.popperRect,\n        popperState = _ref.popperState,\n        props = _ref.props;\n    var interactiveBorder = props.interactiveBorder;\n    var basePlacement = getBasePlacement(popperState.placement);\n    var offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    var topDistance = basePlacement === 'bottom' ? offsetData.top.y : 0;\n    var bottomDistance = basePlacement === 'top' ? offsetData.bottom.y : 0;\n    var leftDistance = basePlacement === 'right' ? offsetData.left.x : 0;\n    var rightDistance = basePlacement === 'left' ? offsetData.right.x : 0;\n    var exceedsTop = popperRect.top - clientY + topDistance > interactiveBorder;\n    var exceedsBottom = clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    var exceedsLeft = popperRect.left - clientX + leftDistance > interactiveBorder;\n    var exceedsRight = clientX - popperRect.right - rightDistance > interactiveBorder;\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\nfunction updateTransitionEndListener(box, action, listener) {\n  var method = action + \"EventListener\"; // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n\n  ['transitionend', 'webkitTransitionEnd'].forEach(function (event) {\n    box[method](event, listener);\n  });\n}\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\n\nfunction actualContains(parent, child) {\n  var target = child;\n\n  while (target) {\n    var _target$getRootNode;\n\n    if (parent.contains(target)) {\n      return true;\n    }\n\n    target = target.getRootNode == null ? void 0 : (_target$getRootNode = target.getRootNode()) == null ? void 0 : _target$getRootNode.host;\n  }\n\n  return false;\n}\n\nvar currentInput = {\n  isTouch: false\n};\nvar lastMouseMoveTime = 0;\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\n\nfunction onDocumentTouchStart() {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\n\nfunction onDocumentMouseMove() {\n  var now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\n\nfunction onWindowBlur() {\n  var activeElement = document.activeElement;\n\n  if (isReferenceElement(activeElement)) {\n    var instance = activeElement._tippy;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\nfunction bindGlobalEventListeners() {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';\nvar isIE11 = isBrowser ? // @ts-ignore\n!!window.msCrypto : false;\n\nfunction createMemoryLeakWarning(method) {\n  var txt = method === 'destroy' ? 'n already-' : ' ';\n  return [method + \"() was called on a\" + txt + \"destroyed instance. This is a no-op but\", 'indicates a potential memory leak.'].join(' ');\n}\nfunction clean(value) {\n  var spacesAndTabs = /[ \\t]{2,}/g;\n  var lineStartWithSpaces = /^[ \\t]*/gm;\n  return value.replace(spacesAndTabs, ' ').replace(lineStartWithSpaces, '').trim();\n}\n\nfunction getDevMessage(message) {\n  return clean(\"\\n  %ctippy.js\\n\\n  %c\" + clean(message) + \"\\n\\n  %c\\uD83D\\uDC77\\u200D This is a development-only message. It will be removed in production.\\n  \");\n}\n\nfunction getFormattedMessage(message) {\n  return [getDevMessage(message), // title\n  'color: #00C584; font-size: 1.3em; font-weight: bold;', // message\n  'line-height: 1.5', // footer\n  'color: #a6a095;'];\n} // Assume warnings and errors never have the same message\n\nvar visitedMessages;\n\nif (true) {\n  resetVisitedMessages();\n}\n\nfunction resetVisitedMessages() {\n  visitedMessages = new Set();\n}\nfunction warnWhen(condition, message) {\n  if (condition && !visitedMessages.has(message)) {\n    var _console;\n\n    visitedMessages.add(message);\n\n    (_console = console).warn.apply(_console, getFormattedMessage(message));\n  }\n}\nfunction errorWhen(condition, message) {\n  if (condition && !visitedMessages.has(message)) {\n    var _console2;\n\n    visitedMessages.add(message);\n\n    (_console2 = console).error.apply(_console2, getFormattedMessage(message));\n  }\n}\nfunction validateTargets(targets) {\n  var didPassFalsyValue = !targets;\n  var didPassPlainObject = Object.prototype.toString.call(targets) === '[object Object]' && !targets.addEventListener;\n  errorWhen(didPassFalsyValue, ['tippy() was passed', '`' + String(targets) + '`', 'as its targets (first) argument. Valid types are: String, Element,', 'Element[], or NodeList.'].join(' '));\n  errorWhen(didPassPlainObject, ['tippy() was passed a plain object which is not supported as an argument', 'for virtual positioning. Use props.getReferenceClientRect instead.'].join(' '));\n}\n\nvar pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false\n};\nvar renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999\n};\nvar defaultProps = Object.assign({\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto'\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate: function onAfterUpdate() {},\n  onBeforeUpdate: function onBeforeUpdate() {},\n  onCreate: function onCreate() {},\n  onDestroy: function onDestroy() {},\n  onHidden: function onHidden() {},\n  onHide: function onHide() {},\n  onMount: function onMount() {},\n  onShow: function onShow() {},\n  onShown: function onShown() {},\n  onTrigger: function onTrigger() {},\n  onUntrigger: function onUntrigger() {},\n  onClickOutside: function onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null\n}, pluginProps, renderProps);\nvar defaultKeys = Object.keys(defaultProps);\nvar setDefaultProps = function setDefaultProps(partialProps) {\n  /* istanbul ignore else */\n  if (true) {\n    validateProps(partialProps, []);\n  }\n\n  var keys = Object.keys(partialProps);\n  keys.forEach(function (key) {\n    defaultProps[key] = partialProps[key];\n  });\n};\nfunction getExtendedPassedProps(passedProps) {\n  var plugins = passedProps.plugins || [];\n  var pluginProps = plugins.reduce(function (acc, plugin) {\n    var name = plugin.name,\n        defaultValue = plugin.defaultValue;\n\n    if (name) {\n      var _name;\n\n      acc[name] = passedProps[name] !== undefined ? passedProps[name] : (_name = defaultProps[name]) != null ? _name : defaultValue;\n    }\n\n    return acc;\n  }, {});\n  return Object.assign({}, passedProps, pluginProps);\n}\nfunction getDataAttributeProps(reference, plugins) {\n  var propKeys = plugins ? Object.keys(getExtendedPassedProps(Object.assign({}, defaultProps, {\n    plugins: plugins\n  }))) : defaultKeys;\n  var props = propKeys.reduce(function (acc, key) {\n    var valueAsString = (reference.getAttribute(\"data-tippy-\" + key) || '').trim();\n\n    if (!valueAsString) {\n      return acc;\n    }\n\n    if (key === 'content') {\n      acc[key] = valueAsString;\n    } else {\n      try {\n        acc[key] = JSON.parse(valueAsString);\n      } catch (e) {\n        acc[key] = valueAsString;\n      }\n    }\n\n    return acc;\n  }, {});\n  return props;\n}\nfunction evaluateProps(reference, props) {\n  var out = Object.assign({}, props, {\n    content: invokeWithArgsOrReturn(props.content, [reference])\n  }, props.ignoreAttributes ? {} : getDataAttributeProps(reference, props.plugins));\n  out.aria = Object.assign({}, defaultProps.aria, out.aria);\n  out.aria = {\n    expanded: out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content: out.aria.content === 'auto' ? props.interactive ? null : 'describedby' : out.aria.content\n  };\n  return out;\n}\nfunction validateProps(partialProps, plugins) {\n  if (partialProps === void 0) {\n    partialProps = {};\n  }\n\n  if (plugins === void 0) {\n    plugins = [];\n  }\n\n  var keys = Object.keys(partialProps);\n  keys.forEach(function (prop) {\n    var nonPluginProps = removeProperties(defaultProps, Object.keys(pluginProps));\n    var didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop); // Check if the prop exists in `plugins`\n\n    if (didPassUnknownProp) {\n      didPassUnknownProp = plugins.filter(function (plugin) {\n        return plugin.name === prop;\n      }).length === 0;\n    }\n\n    warnWhen(didPassUnknownProp, [\"`\" + prop + \"`\", \"is not a valid prop. You may have spelled it incorrectly, or if it's\", 'a plugin, forgot to pass it in an array as props.plugins.', '\\n\\n', 'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n', 'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/'].join(' '));\n  });\n}\n\nvar innerHTML = function innerHTML() {\n  return 'innerHTML';\n};\n\nfunction dangerouslySetInnerHTML(element, html) {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value) {\n  var arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value);\n    }\n  }\n\n  return arrow;\n}\n\nfunction setContent(content, props) {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\nfunction getChildren(popper) {\n  var box = popper.firstElementChild;\n  var boxChildren = arrayFrom(box.children);\n  return {\n    box: box,\n    content: boxChildren.find(function (node) {\n      return node.classList.contains(CONTENT_CLASS);\n    }),\n    arrow: boxChildren.find(function (node) {\n      return node.classList.contains(ARROW_CLASS) || node.classList.contains(SVG_ARROW_CLASS);\n    }),\n    backdrop: boxChildren.find(function (node) {\n      return node.classList.contains(BACKDROP_CLASS);\n    })\n  };\n}\nfunction render(instance) {\n  var popper = div();\n  var box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n  var content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n  setContent(content, instance.props);\n  popper.appendChild(box);\n  box.appendChild(content);\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps, nextProps) {\n    var _getChildren = getChildren(popper),\n        box = _getChildren.box,\n        content = _getChildren.content,\n        arrow = _getChildren.arrow;\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth = typeof nextProps.maxWidth === 'number' ? nextProps.maxWidth + \"px\" : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (prevProps.content !== nextProps.content || prevProps.allowHTML !== nextProps.allowHTML) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow);\n    }\n  }\n\n  return {\n    popper: popper,\n    onUpdate: onUpdate\n  };\n} // Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\n\nrender.$$tippy = true;\n\nvar idCounter = 1;\nvar mouseMoveListeners = []; // Used by `hideAll()`\n\nvar mountedInstances = [];\nfunction createTippy(reference, passedProps) {\n  var props = evaluateProps(reference, Object.assign({}, defaultProps, getExtendedPassedProps(removeUndefinedProps(passedProps)))); // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n\n  var showTimeout;\n  var hideTimeout;\n  var scheduleHideAnimationFrame;\n  var isVisibleFromClick = false;\n  var didHideDueToDocumentMouseDown = false;\n  var didTouchMove = false;\n  var ignoreOnFirstUpdate = false;\n  var lastTriggerEvent;\n  var currentTransitionEndListener;\n  var onFirstUpdate;\n  var listeners = [];\n  var debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  var currentTarget; // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n\n  var id = idCounter++;\n  var popperInstance = null;\n  var plugins = unique(props.plugins);\n  var state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false\n  };\n  var instance = {\n    // properties\n    id: id,\n    reference: reference,\n    popper: div(),\n    popperInstance: popperInstance,\n    props: props,\n    state: state,\n    plugins: plugins,\n    // methods\n    clearDelayTimeouts: clearDelayTimeouts,\n    setProps: setProps,\n    setContent: setContent,\n    show: show,\n    hide: hide,\n    hideWithInteractivity: hideWithInteractivity,\n    enable: enable,\n    disable: disable,\n    unmount: unmount,\n    destroy: destroy\n  }; // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n\n  /* istanbul ignore if */\n\n  if (!props.render) {\n    if (true) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  } // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n\n\n  var _props$render = props.render(instance),\n      popper = _props$render.popper,\n      onUpdate = _props$render.onUpdate;\n\n  popper.setAttribute('data-tippy-root', '');\n  popper.id = \"tippy-\" + instance.id;\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n  var pluginsHooks = plugins.map(function (plugin) {\n    return plugin.fn(instance);\n  });\n  var hasAriaExpanded = reference.hasAttribute('aria-expanded');\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  } // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n\n\n  popper.addEventListener('mouseenter', function () {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n  popper.addEventListener('mouseleave', function () {\n    if (instance.props.interactive && instance.props.trigger.indexOf('mouseenter') >= 0) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n  return instance; // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n\n  function getNormalizedTouchSettings() {\n    var touch = instance.props.touch;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior() {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn() {\n    var _instance$props$rende;\n\n    // @ts-ignore\n    return !!((_instance$props$rende = instance.props.render) != null && _instance$props$rende.$$tippy);\n  }\n\n  function getCurrentTarget() {\n    return currentTarget || reference;\n  }\n\n  function getDocument() {\n    var parent = getCurrentTarget().parentNode;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren() {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow) {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (instance.state.isMounted && !instance.state.isVisible || currentInput.isTouch || lastTriggerEvent && lastTriggerEvent.type === 'focus') {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(instance.props.delay, isShow ? 0 : 1, defaultProps.delay);\n  }\n\n  function handleStyles(fromHide) {\n    if (fromHide === void 0) {\n      fromHide = false;\n    }\n\n    popper.style.pointerEvents = instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = \"\" + instance.props.zIndex;\n  }\n\n  function invokeHook(hook, args, shouldInvokePropsHook) {\n    if (shouldInvokePropsHook === void 0) {\n      shouldInvokePropsHook = true;\n    }\n\n    pluginsHooks.forEach(function (pluginHooks) {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook].apply(pluginHooks, args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      var _instance$props;\n\n      (_instance$props = instance.props)[hook].apply(_instance$props, args);\n    }\n  }\n\n  function handleAriaContentAttribute() {\n    var aria = instance.props.aria;\n\n    if (!aria.content) {\n      return;\n    }\n\n    var attr = \"aria-\" + aria.content;\n    var id = popper.id;\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      var currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? currentValue + \" \" + id : id);\n      } else {\n        var nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute() {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      if (instance.props.interactive) {\n        node.setAttribute('aria-expanded', instance.state.isVisible && node === getCurrentTarget() ? 'true' : 'false');\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners() {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(function (listener) {\n      return listener !== debouncedOnMouseMove;\n    });\n  }\n\n  function onDocumentPress(event) {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    var actualTarget = event.composedPath && event.composedPath()[0] || event.target; // Clicked on interactive popper\n\n    if (instance.props.interactive && actualContains(popper, actualTarget)) {\n      return;\n    } // Clicked on the event listeners target\n\n\n    if (normalizeToArray(instance.props.triggerTarget || reference).some(function (el) {\n      return actualContains(el, actualTarget);\n    })) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (instance.state.isVisible && instance.props.trigger.indexOf('click') >= 0) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide(); // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(function () {\n        didHideDueToDocumentMouseDown = false;\n      }); // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove() {\n    didTouchMove = true;\n  }\n\n  function onTouchStart() {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress() {\n    var doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress() {\n    var doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration, callback) {\n    onTransitionEnd(duration, function () {\n      if (!instance.state.isVisible && popper.parentNode && popper.parentNode.contains(popper)) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration, callback) {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration, callback) {\n    var box = getDefaultTemplateChildren().box;\n\n    function listener(event) {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    } // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n\n\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n    currentTransitionEndListener = listener;\n  }\n\n  function on(eventType, handler, options) {\n    if (options === void 0) {\n      options = false;\n    }\n\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({\n        node: node,\n        eventType: eventType,\n        handler: handler,\n        options: options\n      });\n    });\n  }\n\n  function addListeners() {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {\n        passive: true\n      });\n      on('touchend', onMouseLeave, {\n        passive: true\n      });\n    }\n\n    splitBySpaces(instance.props.trigger).forEach(function (eventType) {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave);\n          break;\n\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut);\n          break;\n\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut);\n          break;\n      }\n    });\n  }\n\n  function removeListeners() {\n    listeners.forEach(function (_ref) {\n      var node = _ref.node,\n          eventType = _ref.eventType,\n          handler = _ref.handler,\n          options = _ref.options;\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event) {\n    var _lastTriggerEvent;\n\n    var shouldScheduleClickHide = false;\n\n    if (!instance.state.isEnabled || isEventListenerStopped(event) || didHideDueToDocumentMouseDown) {\n      return;\n    }\n\n    var wasFocused = ((_lastTriggerEvent = lastTriggerEvent) == null ? void 0 : _lastTriggerEvent.type) === 'focus';\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget;\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach(function (listener) {\n        return listener(event);\n      });\n    } // Toggle show/hide when clicking click-triggered tooltips\n\n\n    if (event.type === 'click' && (instance.props.trigger.indexOf('mouseenter') < 0 || isVisibleFromClick) && instance.props.hideOnClick !== false && instance.state.isVisible) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event) {\n    var target = event.target;\n    var isCursorOverReferenceOrPopper = getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    var popperTreeData = getNestedPopperTree().concat(popper).map(function (popper) {\n      var _instance$popperInsta;\n\n      var instance = popper._tippy;\n      var state = (_instance$popperInsta = instance.popperInstance) == null ? void 0 : _instance$popperInsta.state;\n\n      if (state) {\n        return {\n          popperRect: popper.getBoundingClientRect(),\n          popperState: state,\n          props: props\n        };\n      }\n\n      return null;\n    }).filter(Boolean);\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event) {\n    var shouldBail = isEventListenerStopped(event) || instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick;\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event) {\n    if (instance.props.trigger.indexOf('focusin') < 0 && event.target !== getCurrentTarget()) {\n      return;\n    } // If focus was moved to within the popper\n\n\n    if (instance.props.interactive && event.relatedTarget && popper.contains(event.relatedTarget)) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event) {\n    return currentInput.isTouch ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0 : false;\n  }\n\n  function createPopperInstance() {\n    destroyPopperInstance();\n    var _instance$props2 = instance.props,\n        popperOptions = _instance$props2.popperOptions,\n        placement = _instance$props2.placement,\n        offset = _instance$props2.offset,\n        getReferenceClientRect = _instance$props2.getReferenceClientRect,\n        moveTransition = _instance$props2.moveTransition;\n    var arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n    var computedReference = getReferenceClientRect ? {\n      getBoundingClientRect: getReferenceClientRect,\n      contextElement: getReferenceClientRect.contextElement || getCurrentTarget()\n    } : reference;\n    var tippyModifier = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn: function fn(_ref2) {\n        var state = _ref2.state;\n\n        if (getIsDefaultRenderFn()) {\n          var _getDefaultTemplateCh = getDefaultTemplateChildren(),\n              box = _getDefaultTemplateCh.box;\n\n          ['placement', 'reference-hidden', 'escaped'].forEach(function (attr) {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[\"data-popper-\" + attr]) {\n                box.setAttribute(\"data-\" + attr, '');\n              } else {\n                box.removeAttribute(\"data-\" + attr);\n              }\n            }\n          });\n          state.attributes.popper = {};\n        }\n      }\n    };\n    var modifiers = [{\n      name: 'offset',\n      options: {\n        offset: offset\n      }\n    }, {\n      name: 'preventOverflow',\n      options: {\n        padding: {\n          top: 2,\n          bottom: 2,\n          left: 5,\n          right: 5\n        }\n      }\n    }, {\n      name: 'flip',\n      options: {\n        padding: 5\n      }\n    }, {\n      name: 'computeStyles',\n      options: {\n        adaptive: !moveTransition\n      }\n    }, tippyModifier];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3\n        }\n      });\n    }\n\n    modifiers.push.apply(modifiers, (popperOptions == null ? void 0 : popperOptions.modifiers) || []);\n    instance.popperInstance = (0,_popperjs_core__WEBPACK_IMPORTED_MODULE_0__.createPopper)(computedReference, popper, Object.assign({}, popperOptions, {\n      placement: placement,\n      onFirstUpdate: onFirstUpdate,\n      modifiers: modifiers\n    }));\n  }\n\n  function destroyPopperInstance() {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount() {\n    var appendTo = instance.props.appendTo;\n    var parentNode; // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n\n    var node = getCurrentTarget();\n\n    if (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO || appendTo === 'parent') {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    } // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n\n\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n    createPopperInstance();\n    /* istanbul ignore else */\n\n    if (true) {\n      // Accessibility check\n      warnWhen(instance.props.interactive && appendTo === defaultProps.appendTo && node.nextElementSibling !== popper, ['Interactive tippy element may not be accessible via keyboard', 'navigation because it is not directly after the reference element', 'in the DOM source order.', '\\n\\n', 'Using a wrapper <div> or <span> tag around the reference element', 'solves this by creating a new parentNode context.', '\\n\\n', 'Specifying `appendTo: document.body` silences this warning, but it', 'assumes you are using a focus management solution to handle', 'keyboard navigation.', '\\n\\n', 'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity'].join(' '));\n    }\n  }\n\n  function getNestedPopperTree() {\n    return arrayFrom(popper.querySelectorAll('[data-tippy-root]'));\n  }\n\n  function scheduleShow(event) {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n    var delay = getDelay(true);\n\n    var _getNormalizedTouchSe = getNormalizedTouchSettings(),\n        touchValue = _getNormalizedTouchSe[0],\n        touchDelay = _getNormalizedTouchSe[1];\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(function () {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event) {\n    instance.clearDelayTimeouts();\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n      return;\n    } // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n\n\n    if (instance.props.trigger.indexOf('mouseenter') >= 0 && instance.props.trigger.indexOf('click') >= 0 && ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 && isVisibleFromClick) {\n      return;\n    }\n\n    var delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(function () {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(function () {\n        instance.hide();\n      });\n    }\n  } // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n\n\n  function enable() {\n    instance.state.isEnabled = true;\n  }\n\n  function disable() {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts() {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps) {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n    removeListeners();\n    var prevProps = instance.props;\n    var nextProps = evaluateProps(reference, Object.assign({}, prevProps, removeUndefinedProps(partialProps), {\n      ignoreAttributes: true\n    }));\n    instance.props = nextProps;\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(onMouseMove, nextProps.interactiveDebounce);\n    } // Ensure stale aria-expanded attributes are removed\n\n\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach(function (node) {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance(); // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n\n      getNestedPopperTree().forEach(function (nestedPopper) {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy.popperInstance.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content) {\n    instance.setProps({\n      content: content\n    });\n  }\n\n  function show() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    } // Early bail-out\n\n\n    var isAlreadyVisible = instance.state.isVisible;\n    var isDestroyed = instance.state.isDestroyed;\n    var isDisabled = !instance.state.isEnabled;\n    var isTouchAndTouchDisabled = currentInput.isTouch && !instance.props.touch;\n    var duration = getValueAtIndexOrReturn(instance.props.duration, 0, defaultProps.duration);\n\n    if (isAlreadyVisible || isDestroyed || isDisabled || isTouchAndTouchDisabled) {\n      return;\n    } // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n\n\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    } // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n\n\n    if (getIsDefaultRenderFn()) {\n      var _getDefaultTemplateCh2 = getDefaultTemplateChildren(),\n          box = _getDefaultTemplateCh2.box,\n          content = _getDefaultTemplateCh2.content;\n\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = function onFirstUpdate() {\n      var _instance$popperInsta2;\n\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true; // reflow\n\n      void popper.offsetHeight;\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        var _getDefaultTemplateCh3 = getDefaultTemplateChildren(),\n            _box = _getDefaultTemplateCh3.box,\n            _content = _getDefaultTemplateCh3.content;\n\n        setTransitionDuration([_box, _content], duration);\n        setVisibilityState([_box, _content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n      pushIfUnique(mountedInstances, instance); // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n\n      (_instance$popperInsta2 = instance.popperInstance) == null ? void 0 : _instance$popperInsta2.forceUpdate();\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, function () {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    } // Early bail-out\n\n\n    var isAlreadyHidden = !instance.state.isVisible;\n    var isDestroyed = instance.state.isDestroyed;\n    var isDisabled = !instance.state.isEnabled;\n    var duration = getValueAtIndexOrReturn(instance.props.duration, 1, defaultProps.duration);\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      var _getDefaultTemplateCh4 = getDefaultTemplateChildren(),\n          box = _getDefaultTemplateCh4.box,\n          content = _getDefaultTemplateCh4.content;\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event) {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hideWithInteractivity'));\n    }\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance(); // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n\n    getNestedPopperTree().forEach(function (nestedPopper) {\n      nestedPopper._tippy.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter(function (i) {\n      return i !== instance;\n    });\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n    removeListeners();\n    delete reference._tippy;\n    instance.state.isDestroyed = true;\n    invokeHook('onDestroy', [instance]);\n  }\n}\n\nfunction tippy(targets, optionalProps) {\n  if (optionalProps === void 0) {\n    optionalProps = {};\n  }\n\n  var plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n  /* istanbul ignore else */\n\n  if (true) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n  var passedProps = Object.assign({}, optionalProps, {\n    plugins: plugins\n  });\n  var elements = getArrayOfElements(targets);\n  /* istanbul ignore else */\n\n  if (true) {\n    var isSingleContentElement = isElement(passedProps.content);\n    var isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(isSingleContentElement && isMoreThanOneReferenceElement, ['tippy() was passed an Element as the `content` prop, but more than', 'one tippy instance was created by this invocation. This means the', 'content element will only be appended to the last tippy instance.', '\\n\\n', 'Instead, pass the .innerHTML of the element, or use a function that', 'returns a cloned version of the element instead.', '\\n\\n', '1) content: element.innerHTML\\n', '2) content: () => element.cloneNode(true)'].join(' '));\n  }\n\n  var instances = elements.reduce(function (acc, reference) {\n    var instance = reference && createTippy(reference, passedProps);\n\n    if (instance) {\n      acc.push(instance);\n    }\n\n    return acc;\n  }, []);\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\nvar hideAll = function hideAll(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      excludedReferenceOrInstance = _ref.exclude,\n      duration = _ref.duration;\n\n  mountedInstances.forEach(function (instance) {\n    var isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance) ? instance.reference === excludedReferenceOrInstance : instance.popper === excludedReferenceOrInstance.popper;\n    }\n\n    if (!isExcluded) {\n      var originalDuration = instance.props.duration;\n      instance.setProps({\n        duration: duration\n      });\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({\n          duration: originalDuration\n        });\n      }\n    }\n  });\n};\n\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\n\nvar applyStylesModifier = Object.assign({}, _popperjs_core__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n  effect: function effect(_ref) {\n    var state = _ref.state;\n    var initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0'\n      },\n      arrow: {\n        position: 'absolute'\n      },\n      reference: {}\n    };\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    } // intentionally return no cleanup function\n    // return () => { ... }\n\n  }\n});\n\nvar createSingleton = function createSingleton(tippyInstances, optionalProps) {\n  var _optionalProps$popper;\n\n  if (optionalProps === void 0) {\n    optionalProps = {};\n  }\n\n  /* istanbul ignore else */\n  if (true) {\n    errorWhen(!Array.isArray(tippyInstances), ['The first argument passed to createSingleton() must be an array of', 'tippy instances. The passed value was', String(tippyInstances)].join(' '));\n  }\n\n  var individualInstances = tippyInstances;\n  var references = [];\n  var triggerTargets = [];\n  var currentTarget;\n  var overrides = optionalProps.overrides;\n  var interceptSetPropsCleanups = [];\n  var shownOnCreate = false;\n\n  function setTriggerTargets() {\n    triggerTargets = individualInstances.map(function (instance) {\n      return normalizeToArray(instance.props.triggerTarget || instance.reference);\n    }).reduce(function (acc, item) {\n      return acc.concat(item);\n    }, []);\n  }\n\n  function setReferences() {\n    references = individualInstances.map(function (instance) {\n      return instance.reference;\n    });\n  }\n\n  function enableInstances(isEnabled) {\n    individualInstances.forEach(function (instance) {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton) {\n    return individualInstances.map(function (instance) {\n      var originalSetProps = instance.setProps;\n\n      instance.setProps = function (props) {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return function () {\n        instance.setProps = originalSetProps;\n      };\n    });\n  } // have to pass singleton, as it maybe undefined on first call\n\n\n  function prepareInstance(singleton, target) {\n    var index = triggerTargets.indexOf(target); // bail-out\n\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n    var overrideProps = (overrides || []).concat('content').reduce(function (acc, prop) {\n      acc[prop] = individualInstances[index].props[prop];\n      return acc;\n    }, {});\n    singleton.setProps(Object.assign({}, overrideProps, {\n      getReferenceClientRect: typeof overrideProps.getReferenceClientRect === 'function' ? overrideProps.getReferenceClientRect : function () {\n        var _references$index;\n\n        return (_references$index = references[index]) == null ? void 0 : _references$index.getBoundingClientRect();\n      }\n    }));\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n  var plugin = {\n    fn: function fn() {\n      return {\n        onDestroy: function onDestroy() {\n          enableInstances(true);\n        },\n        onHidden: function onHidden() {\n          currentTarget = null;\n        },\n        onClickOutside: function onClickOutside(instance) {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow: function onShow(instance) {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger: function onTrigger(instance, event) {\n          prepareInstance(instance, event.currentTarget);\n        }\n      };\n    }\n  };\n  var singleton = tippy(div(), Object.assign({}, removeProperties(optionalProps, ['overrides']), {\n    plugins: [plugin].concat(optionalProps.plugins || []),\n    triggerTarget: triggerTargets,\n    popperOptions: Object.assign({}, optionalProps.popperOptions, {\n      modifiers: [].concat(((_optionalProps$popper = optionalProps.popperOptions) == null ? void 0 : _optionalProps$popper.modifiers) || [], [applyStylesModifier])\n    })\n  }));\n  var originalShow = singleton.show;\n\n  singleton.show = function (target) {\n    originalShow(); // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    } // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n\n\n    if (currentTarget && target == null) {\n      return;\n    } // target is index of instance\n\n\n    if (typeof target === 'number') {\n      return references[target] && prepareInstance(singleton, references[target]);\n    } // target is a child tippy instance\n\n\n    if (individualInstances.indexOf(target) >= 0) {\n      var ref = target.reference;\n      return prepareInstance(singleton, ref);\n    } // target is a ReferenceElement\n\n\n    if (references.indexOf(target) >= 0) {\n      return prepareInstance(singleton, target);\n    }\n  };\n\n  singleton.showNext = function () {\n    var first = references[0];\n\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n\n    var index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = function () {\n    var last = references[references.length - 1];\n\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n\n    var index = references.indexOf(currentTarget);\n    var target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  var originalSetProps = singleton.setProps;\n\n  singleton.setProps = function (props) {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = function (nextInstances) {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach(function (fn) {\n      return fn();\n    });\n    individualInstances = nextInstances;\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n    singleton.setProps({\n      triggerTarget: triggerTargets\n    });\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n  return singleton;\n};\n\nvar BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click'\n};\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\n\nfunction delegate(targets, props) {\n  /* istanbul ignore else */\n  if (true) {\n    errorWhen(!(props && props.target), ['You must specity a `target` prop indicating a CSS selector string matching', 'the target elements that should receive a tippy.'].join(' '));\n  }\n\n  var listeners = [];\n  var childTippyInstances = [];\n  var disabled = false;\n  var target = props.target;\n  var nativeProps = removeProperties(props, ['target']);\n  var parentProps = Object.assign({}, nativeProps, {\n    trigger: 'manual',\n    touch: false\n  });\n  var childProps = Object.assign({\n    touch: defaultProps.touch\n  }, nativeProps, {\n    showOnCreate: true\n  });\n  var returnValue = tippy(targets, parentProps);\n  var normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event) {\n    if (!event.target || disabled) {\n      return;\n    }\n\n    var targetNode = event.target.closest(target);\n\n    if (!targetNode) {\n      return;\n    } // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n\n\n    var trigger = targetNode.getAttribute('data-tippy-trigger') || props.trigger || defaultProps.trigger; // @ts-ignore\n\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (event.type !== 'touchstart' && trigger.indexOf(BUBBLING_EVENTS_MAP[event.type]) < 0) {\n      return;\n    }\n\n    var instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(node, eventType, handler, options) {\n    if (options === void 0) {\n      options = false;\n    }\n\n    node.addEventListener(eventType, handler, options);\n    listeners.push({\n      node: node,\n      eventType: eventType,\n      handler: handler,\n      options: options\n    });\n  }\n\n  function addEventListeners(instance) {\n    var reference = instance.reference;\n    on(reference, 'touchstart', onTrigger, TOUCH_OPTIONS);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners() {\n    listeners.forEach(function (_ref) {\n      var node = _ref.node,\n          eventType = _ref.eventType,\n          handler = _ref.handler,\n          options = _ref.options;\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance) {\n    var originalDestroy = instance.destroy;\n    var originalEnable = instance.enable;\n    var originalDisable = instance.disable;\n\n    instance.destroy = function (shouldDestroyChildInstances) {\n      if (shouldDestroyChildInstances === void 0) {\n        shouldDestroyChildInstances = true;\n      }\n\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach(function (instance) {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    instance.enable = function () {\n      originalEnable();\n      childTippyInstances.forEach(function (instance) {\n        return instance.enable();\n      });\n      disabled = false;\n    };\n\n    instance.disable = function () {\n      originalDisable();\n      childTippyInstances.forEach(function (instance) {\n        return instance.disable();\n      });\n      disabled = true;\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n  return returnValue;\n}\n\nvar animateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var _instance$props$rende;\n\n    // @ts-ignore\n    if (!((_instance$props$rende = instance.props.render) != null && _instance$props$rende.$$tippy)) {\n      if (true) {\n        errorWhen(instance.props.animateFill, 'The `animateFill` plugin requires the default render function.');\n      }\n\n      return {};\n    }\n\n    var _getChildren = getChildren(instance.popper),\n        box = _getChildren.box,\n        content = _getChildren.content;\n\n    var backdrop = instance.props.animateFill ? createBackdropElement() : null;\n    return {\n      onCreate: function onCreate() {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n          instance.setProps({\n            arrow: false,\n            animation: 'shift-away'\n          });\n        }\n      },\n      onMount: function onMount() {\n        if (backdrop) {\n          var transitionDuration = box.style.transitionDuration;\n          var duration = Number(transitionDuration.replace('ms', '')); // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n\n          content.style.transitionDelay = Math.round(duration / 10) + \"ms\";\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow: function onShow() {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide: function onHide() {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      }\n    };\n  }\n};\n\nfunction createBackdropElement() {\n  var backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n\nvar mouseCoords = {\n  clientX: 0,\n  clientY: 0\n};\nvar activeInstances = [];\n\nfunction storeMouseCoords(_ref) {\n  var clientX = _ref.clientX,\n      clientY = _ref.clientY;\n  mouseCoords = {\n    clientX: clientX,\n    clientY: clientY\n  };\n}\n\nfunction addMouseCoordsListener(doc) {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc) {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nvar followCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference;\n    var doc = getOwnerDocument(instance.props.triggerTarget || reference);\n    var isInternalUpdate = false;\n    var wasFocusEvent = false;\n    var isUnmounted = true;\n    var prevProps = instance.props;\n\n    function getIsInitialBehavior() {\n      return instance.props.followCursor === 'initial' && instance.state.isVisible;\n    }\n\n    function addListener() {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener() {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect() {\n      isInternalUpdate = true;\n      instance.setProps({\n        getReferenceClientRect: null\n      });\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event) {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      var isCursorOverReference = event.target ? reference.contains(event.target) : true;\n      var followCursor = instance.props.followCursor;\n      var clientX = event.clientX,\n          clientY = event.clientY;\n      var rect = reference.getBoundingClientRect();\n      var relativeX = clientX - rect.left;\n      var relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect: function getReferenceClientRect() {\n            var rect = reference.getBoundingClientRect();\n            var x = clientX;\n            var y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            var top = followCursor === 'horizontal' ? rect.top : y;\n            var right = followCursor === 'vertical' ? rect.right : x;\n            var bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            var left = followCursor === 'vertical' ? rect.left : x;\n            return {\n              width: right - left,\n              height: bottom - top,\n              top: top,\n              right: right,\n              bottom: bottom,\n              left: left\n            };\n          }\n        });\n      }\n    }\n\n    function create() {\n      if (instance.props.followCursor) {\n        activeInstances.push({\n          instance: instance,\n          doc: doc\n        });\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy() {\n      activeInstances = activeInstances.filter(function (data) {\n        return data.instance !== instance;\n      });\n\n      if (activeInstances.filter(function (data) {\n        return data.doc === doc;\n      }).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate: function onBeforeUpdate() {\n        prevProps = instance.props;\n      },\n      onAfterUpdate: function onAfterUpdate(_, _ref2) {\n        var followCursor = _ref2.followCursor;\n\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (followCursor !== undefined && prevProps.followCursor !== followCursor) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (instance.state.isMounted && !wasFocusEvent && !getIsInitialBehavior()) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount: function onMount() {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger: function onTrigger(_, event) {\n        if (isMouseEvent(event)) {\n          mouseCoords = {\n            clientX: event.clientX,\n            clientY: event.clientY\n          };\n        }\n\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden: function onHidden() {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      }\n    };\n  }\n};\n\nfunction getProps(props, modifier) {\n  var _props$popperOptions;\n\n  return {\n    popperOptions: Object.assign({}, props.popperOptions, {\n      modifiers: [].concat((((_props$popperOptions = props.popperOptions) == null ? void 0 : _props$popperOptions.modifiers) || []).filter(function (_ref) {\n        var name = _ref.name;\n        return name !== modifier.name;\n      }), [modifier])\n    })\n  };\n}\n\nvar inlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference;\n\n    function isEnabled() {\n      return !!instance.props.inlinePositioning;\n    }\n\n    var placement;\n    var cursorRectIndex = -1;\n    var isInternalUpdate = false;\n    var triedPlacements = [];\n    var modifier = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn: function fn(_ref2) {\n        var state = _ref2.state;\n\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (placement !== state.placement && triedPlacements.indexOf(state.placement) === -1) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: function getReferenceClientRect() {\n                return _getReferenceClientRect(state.placement);\n              }\n            });\n          }\n\n          placement = state.placement;\n        }\n      }\n    };\n\n    function _getReferenceClientRect(placement) {\n      return getInlineBoundingClientRect(getBasePlacement(placement), reference.getBoundingClientRect(), arrayFrom(reference.getClientRects()), cursorRectIndex);\n    }\n\n    function setInternalProps(partialProps) {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier() {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger: function onTrigger(_, event) {\n        if (isMouseEvent(event)) {\n          var rects = arrayFrom(instance.reference.getClientRects());\n          var cursorRect = rects.find(function (rect) {\n            return rect.left - 2 <= event.clientX && rect.right + 2 >= event.clientX && rect.top - 2 <= event.clientY && rect.bottom + 2 >= event.clientY;\n          });\n          var index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden: function onHidden() {\n        cursorRectIndex = -1;\n      }\n    };\n  }\n};\nfunction getInlineBoundingClientRect(currentBasePlacement, boundingRect, clientRects, cursorRectIndex) {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  } // There are two rects and they are disjoined\n\n\n  if (clientRects.length === 2 && cursorRectIndex >= 0 && clientRects[0].left > clientRects[1].right) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom':\n      {\n        var firstRect = clientRects[0];\n        var lastRect = clientRects[clientRects.length - 1];\n        var isTop = currentBasePlacement === 'top';\n        var top = firstRect.top;\n        var bottom = lastRect.bottom;\n        var left = isTop ? firstRect.left : lastRect.left;\n        var right = isTop ? firstRect.right : lastRect.right;\n        var width = right - left;\n        var height = bottom - top;\n        return {\n          top: top,\n          bottom: bottom,\n          left: left,\n          right: right,\n          width: width,\n          height: height\n        };\n      }\n\n    case 'left':\n    case 'right':\n      {\n        var minLeft = Math.min.apply(Math, clientRects.map(function (rects) {\n          return rects.left;\n        }));\n        var maxRight = Math.max.apply(Math, clientRects.map(function (rects) {\n          return rects.right;\n        }));\n        var measureRects = clientRects.filter(function (rect) {\n          return currentBasePlacement === 'left' ? rect.left === minLeft : rect.right === maxRight;\n        });\n        var _top = measureRects[0].top;\n        var _bottom = measureRects[measureRects.length - 1].bottom;\n        var _left = minLeft;\n        var _right = maxRight;\n\n        var _width = _right - _left;\n\n        var _height = _bottom - _top;\n\n        return {\n          top: _top,\n          bottom: _bottom,\n          left: _left,\n          right: _right,\n          width: _width,\n          height: _height\n        };\n      }\n\n    default:\n      {\n        return boundingRect;\n      }\n  }\n}\n\nvar sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference,\n        popper = instance.popper;\n\n    function getReference() {\n      return instance.popperInstance ? instance.popperInstance.state.elements.reference : reference;\n    }\n\n    function shouldCheck(value) {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    var prevRefRect = null;\n    var prevPopRect = null;\n\n    function updatePosition() {\n      var currentRefRect = shouldCheck('reference') ? getReference().getBoundingClientRect() : null;\n      var currentPopRect = shouldCheck('popper') ? popper.getBoundingClientRect() : null;\n\n      if (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect) || currentPopRect && areRectsDifferent(prevPopRect, currentPopRect)) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount: function onMount() {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      }\n    };\n  }\n};\n\nfunction areRectsDifferent(rectA, rectB) {\n  if (rectA && rectB) {\n    return rectA.top !== rectB.top || rectA.right !== rectB.right || rectA.bottom !== rectB.bottom || rectA.left !== rectB.left;\n  }\n\n  return true;\n}\n\ntippy.setDefaultProps({\n  render: render\n});\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tippy);\n\n//# sourceMappingURL=tippy.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tippy.js/dist/tippy.esm.js\n");

/***/ })

};
;