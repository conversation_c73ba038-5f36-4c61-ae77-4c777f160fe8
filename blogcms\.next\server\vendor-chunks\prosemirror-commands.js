"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-commands";
exports.ids = ["vendor-chunks/prosemirror-commands"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-commands/dist/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/prosemirror-commands/dist/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autoJoin: () => (/* binding */ autoJoin),\n/* harmony export */   baseKeymap: () => (/* binding */ baseKeymap),\n/* harmony export */   chainCommands: () => (/* binding */ chainCommands),\n/* harmony export */   createParagraphNear: () => (/* binding */ createParagraphNear),\n/* harmony export */   deleteSelection: () => (/* binding */ deleteSelection),\n/* harmony export */   exitCode: () => (/* binding */ exitCode),\n/* harmony export */   joinBackward: () => (/* binding */ joinBackward),\n/* harmony export */   joinDown: () => (/* binding */ joinDown),\n/* harmony export */   joinForward: () => (/* binding */ joinForward),\n/* harmony export */   joinTextblockBackward: () => (/* binding */ joinTextblockBackward),\n/* harmony export */   joinTextblockForward: () => (/* binding */ joinTextblockForward),\n/* harmony export */   joinUp: () => (/* binding */ joinUp),\n/* harmony export */   lift: () => (/* binding */ lift),\n/* harmony export */   liftEmptyBlock: () => (/* binding */ liftEmptyBlock),\n/* harmony export */   macBaseKeymap: () => (/* binding */ macBaseKeymap),\n/* harmony export */   newlineInCode: () => (/* binding */ newlineInCode),\n/* harmony export */   pcBaseKeymap: () => (/* binding */ pcBaseKeymap),\n/* harmony export */   selectAll: () => (/* binding */ selectAll),\n/* harmony export */   selectNodeBackward: () => (/* binding */ selectNodeBackward),\n/* harmony export */   selectNodeForward: () => (/* binding */ selectNodeForward),\n/* harmony export */   selectParentNode: () => (/* binding */ selectParentNode),\n/* harmony export */   selectTextblockEnd: () => (/* binding */ selectTextblockEnd),\n/* harmony export */   selectTextblockStart: () => (/* binding */ selectTextblockStart),\n/* harmony export */   setBlockType: () => (/* binding */ setBlockType),\n/* harmony export */   splitBlock: () => (/* binding */ splitBlock),\n/* harmony export */   splitBlockAs: () => (/* binding */ splitBlockAs),\n/* harmony export */   splitBlockKeepMarks: () => (/* binding */ splitBlockKeepMarks),\n/* harmony export */   toggleMark: () => (/* binding */ toggleMark),\n/* harmony export */   wrapIn: () => (/* binding */ wrapIn)\n/* harmony export */ });\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n\n\n\n\n/**\nDelete the selection, if there is one.\n*/\nconst deleteSelection = (state, dispatch) => {\n    if (state.selection.empty)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.deleteSelection().scrollIntoView());\n    return true;\n};\nfunction atBlockStart(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"backward\", state)\n        : $cursor.parentOffset > 0))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and at the start of a textblock, try to\nreduce the distance between that block and the one before it—if\nthere's a block directly before it that can be joined, join them.\nIf not, try to move the selected block closer to the next one in\nthe document structure by lifting it out of its parent or moving it\ninto a parent of the previous block. Will use the view for accurate\n(bidi-aware) start-of-textblock detection if given.\n*/\nconst joinBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    // If there is no node before this, try to lift\n    if (!$cut) {\n        let range = $cursor.blockRange(), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n        if (target == null)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    let before = $cut.nodeBefore;\n    // Apply the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, -1))\n        return true;\n    // If the node below has no content and the node above is\n    // selectable, delete the node below and select the one above.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(before, \"end\") || prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(before))) {\n        for (let depth = $cursor.depth;; depth--) {\n            let delStep = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, $cursor.before(depth), $cursor.after(depth), prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n            if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n                if (dispatch) {\n                    let tr = state.tr.step(delStep);\n                    tr.setSelection(textblockAt(before, \"end\")\n                        ? prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos, -1)), -1)\n                        : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, $cut.pos - before.nodeSize));\n                    dispatch(tr.scrollIntoView());\n                }\n                return true;\n            }\n            if (depth == 1 || $cursor.node(depth - 1).childCount > 1)\n                break;\n        }\n    }\n    // If the node before is an atom, delete it\n    if (before.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos - before.nodeSize, $cut.pos).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nA more limited form of [`joinBackward`](https://prosemirror.net/docs/ref/#commands.joinBackward)\nthat only tries to join the current textblock to the one before\nit, if the cursor is at the start of a textblock.\n*/\nconst joinTextblockBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\n/**\nA more limited form of [`joinForward`](https://prosemirror.net/docs/ref/#commands.joinForward)\nthat only tries to join the current textblock to the one after\nit, if the cursor is at the end of a textblock.\n*/\nconst joinTextblockForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\nfunction joinTextblocksAround(state, $cut, dispatch) {\n    let before = $cut.nodeBefore, beforeText = before, beforePos = $cut.pos - 1;\n    for (; !beforeText.isTextblock; beforePos--) {\n        if (beforeText.type.spec.isolating)\n            return false;\n        let child = beforeText.lastChild;\n        if (!child)\n            return false;\n        beforeText = child;\n    }\n    let after = $cut.nodeAfter, afterText = after, afterPos = $cut.pos + 1;\n    for (; !afterText.isTextblock; afterPos++) {\n        if (afterText.type.spec.isolating)\n            return false;\n        let child = afterText.firstChild;\n        if (!child)\n            return false;\n        afterText = child;\n    }\n    let step = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, beforePos, afterPos, prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n    if (!step || step.from != beforePos ||\n        step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceStep && step.slice.size >= afterPos - beforePos)\n        return false;\n    if (dispatch) {\n        let tr = state.tr.step(step);\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(tr.doc, beforePos));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n}\nfunction textblockAt(node, side, only = false) {\n    for (let scan = node; scan; scan = (side == \"start\" ? scan.firstChild : scan.lastChild)) {\n        if (scan.isTextblock)\n            return true;\n        if (only && scan.childCount != 1)\n            return false;\n    }\n    return false;\n}\n/**\nWhen the selection is empty and at the start of a textblock, select\nthe node before that textblock, if possible. This is intended to be\nbound to keys like backspace, after\n[`joinBackward`](https://prosemirror.net/docs/ref/#commands.joinBackward) or other deleting\ncommands, as a fall-back behavior when the schema doesn't allow\ndeletion at the selected point.\n*/\nconst selectNodeBackward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"backward\", state) : $head.parentOffset > 0)\n            return false;\n        $cut = findCutBefore($head);\n    }\n    let node = $cut && $cut.nodeBefore;\n    if (!node || !prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, $cut.pos - node.nodeSize)).scrollIntoView());\n    return true;\n};\nfunction findCutBefore($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            if ($pos.index(i) > 0)\n                return $pos.doc.resolve($pos.before(i + 1));\n            if ($pos.node(i).type.spec.isolating)\n                break;\n        }\n    return null;\n}\nfunction atBlockEnd(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"forward\", state)\n        : $cursor.parentOffset < $cursor.parent.content.size))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and the cursor is at the end of a\ntextblock, try to reduce or remove the boundary between that block\nand the one after it, either by joining them or by moving the other\nblock closer to this one in the tree structure. Will use the view\nfor accurate start-of-textblock detection if given.\n*/\nconst joinForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    // If there is no node after this, there's nothing to do\n    if (!$cut)\n        return false;\n    let after = $cut.nodeAfter;\n    // Try the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, 1))\n        return true;\n    // If the node above has no content and the node below is\n    // selectable, delete the node above and select the one below.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(after, \"start\") || prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(after))) {\n        let delStep = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, $cursor.before(), $cursor.after(), prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n        if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n            if (dispatch) {\n                let tr = state.tr.step(delStep);\n                tr.setSelection(textblockAt(after, \"start\") ? prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos)), 1)\n                    : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, tr.mapping.map($cut.pos)));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    // If the next node is an atom, delete it\n    if (after.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos, $cut.pos + after.nodeSize).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nWhen the selection is empty and at the end of a textblock, select\nthe node coming after that textblock, if possible. This is intended\nto be bound to keys like delete, after\n[`joinForward`](https://prosemirror.net/docs/ref/#commands.joinForward) and similar deleting\ncommands, to provide a fall-back behavior when the schema doesn't\nallow deletion at the selected point.\n*/\nconst selectNodeForward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"forward\", state) : $head.parentOffset < $head.parent.content.size)\n            return false;\n        $cut = findCutAfter($head);\n    }\n    let node = $cut && $cut.nodeAfter;\n    if (!node || !prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, $cut.pos)).scrollIntoView());\n    return true;\n};\nfunction findCutAfter($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            let parent = $pos.node(i);\n            if ($pos.index(i) + 1 < parent.childCount)\n                return $pos.doc.resolve($pos.after(i + 1));\n            if (parent.type.spec.isolating)\n                break;\n        }\n    return null;\n}\n/**\nJoin the selected block or, if there is a text selection, the\nclosest ancestor block of the selection that can be joined, with\nthe sibling above it.\n*/\nconst joinUp = (state, dispatch) => {\n    let sel = state.selection, nodeSel = sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection, point;\n    if (nodeSel) {\n        if (sel.node.isTextblock || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, sel.from))\n            return false;\n        point = sel.from;\n    }\n    else {\n        point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.joinPoint)(state.doc, sel.from, -1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch) {\n        let tr = state.tr.join(point);\n        if (nodeSel)\n            tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, point - state.doc.resolve(point).nodeBefore.nodeSize));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nJoin the selected block, or the closest ancestor of the selection\nthat can be joined, with the sibling after it.\n*/\nconst joinDown = (state, dispatch) => {\n    let sel = state.selection, point;\n    if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection) {\n        if (sel.node.isTextblock || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, sel.to))\n            return false;\n        point = sel.to;\n    }\n    else {\n        point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.joinPoint)(state.doc, sel.to, 1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch)\n        dispatch(state.tr.join(point).scrollIntoView());\n    return true;\n};\n/**\nLift the selected block, or the closest ancestor block of the\nselection that can be lifted, out of its parent node.\n*/\nconst lift = (state, dispatch) => {\n    let { $from, $to } = state.selection;\n    let range = $from.blockRange($to), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nIf the selection is in a node whose type has a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, replace the\nselection with a newline character.\n*/\nconst newlineInCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.insertText(\"\\n\").scrollIntoView());\n    return true;\n};\nfunction defaultBlockAt(match) {\n    for (let i = 0; i < match.edgeCount; i++) {\n        let { type } = match.edge(i);\n        if (type.isTextblock && !type.hasRequiredAttrs())\n            return type;\n    }\n    return null;\n}\n/**\nWhen the selection is in a node with a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, create a\ndefault block after the code block, and move the cursor there.\n*/\nconst exitCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    let above = $head.node(-1), after = $head.indexAfter(-1), type = defaultBlockAt(above.contentMatchAt(after));\n    if (!type || !above.canReplaceWith(after, after, type))\n        return false;\n    if (dispatch) {\n        let pos = $head.after(), tr = state.tr.replaceWith(pos, pos, type.createAndFill());\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.near(tr.doc.resolve(pos), 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf a block node is selected, create an empty paragraph before (if\nit is its parent's first child) or after it.\n*/\nconst createParagraphNear = (state, dispatch) => {\n    let sel = state.selection, { $from, $to } = sel;\n    if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection || $from.parent.inlineContent || $to.parent.inlineContent)\n        return false;\n    let type = defaultBlockAt($to.parent.contentMatchAt($to.indexAfter()));\n    if (!type || !type.isTextblock)\n        return false;\n    if (dispatch) {\n        let side = (!$from.parentOffset && $to.index() < $to.parent.childCount ? $from : $to).pos;\n        let tr = state.tr.insert(side, type.createAndFill());\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(tr.doc, side + 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf the cursor is in an empty textblock that can be lifted, lift the\nblock.\n*/\nconst liftEmptyBlock = (state, dispatch) => {\n    let { $cursor } = state.selection;\n    if (!$cursor || $cursor.parent.content.size)\n        return false;\n    if ($cursor.depth > 1 && $cursor.after() != $cursor.end(-1)) {\n        let before = $cursor.before();\n        if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(state.doc, before)) {\n            if (dispatch)\n                dispatch(state.tr.split(before).scrollIntoView());\n            return true;\n        }\n    }\n    let range = $cursor.blockRange(), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nCreate a variant of [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock) that uses\na custom function to determine the type of the newly split off block.\n*/\nfunction splitBlockAs(splitNode) {\n    return (state, dispatch) => {\n        let { $from, $to } = state.selection;\n        if (state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection && state.selection.node.isBlock) {\n            if (!$from.parentOffset || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(state.doc, $from.pos))\n                return false;\n            if (dispatch)\n                dispatch(state.tr.split($from.pos).scrollIntoView());\n            return true;\n        }\n        if (!$from.depth)\n            return false;\n        let types = [];\n        let splitDepth, deflt, atEnd = false, atStart = false;\n        for (let d = $from.depth;; d--) {\n            let node = $from.node(d);\n            if (node.isBlock) {\n                atEnd = $from.end(d) == $from.pos + ($from.depth - d);\n                atStart = $from.start(d) == $from.pos - ($from.depth - d);\n                deflt = defaultBlockAt($from.node(d - 1).contentMatchAt($from.indexAfter(d - 1)));\n                let splitType = splitNode && splitNode($to.parent, atEnd, $from);\n                types.unshift(splitType || (atEnd && deflt ? { type: deflt } : null));\n                splitDepth = d;\n                break;\n            }\n            else {\n                if (d == 1)\n                    return false;\n                types.unshift(null);\n            }\n        }\n        let tr = state.tr;\n        if (state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection || state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection)\n            tr.deleteSelection();\n        let splitPos = tr.mapping.map($from.pos);\n        let can = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(tr.doc, splitPos, types.length, types);\n        if (!can) {\n            types[0] = deflt ? { type: deflt } : null;\n            can = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(tr.doc, splitPos, types.length, types);\n        }\n        if (!can)\n            return false;\n        tr.split(splitPos, types.length, types);\n        if (!atEnd && atStart && $from.node(splitDepth).type != deflt) {\n            let first = tr.mapping.map($from.before(splitDepth)), $first = tr.doc.resolve(first);\n            if (deflt && $from.node(splitDepth - 1).canReplaceWith($first.index(), $first.index() + 1, deflt))\n                tr.setNodeMarkup(tr.mapping.map($from.before(splitDepth)), deflt);\n        }\n        if (dispatch)\n            dispatch(tr.scrollIntoView());\n        return true;\n    };\n}\n/**\nSplit the parent block of the selection. If the selection is a text\nselection, also delete its content.\n*/\nconst splitBlock = splitBlockAs();\n/**\nActs like [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock), but without\nresetting the set of active marks at the cursor.\n*/\nconst splitBlockKeepMarks = (state, dispatch) => {\n    return splitBlock(state, dispatch && (tr => {\n        let marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks());\n        if (marks)\n            tr.ensureMarks(marks);\n        dispatch(tr);\n    }));\n};\n/**\nMove the selection to the node wrapping the current selection, if\nany. (Will not select the document node.)\n*/\nconst selectParentNode = (state, dispatch) => {\n    let { $from, to } = state.selection, pos;\n    let same = $from.sharedDepth(to);\n    if (same == 0)\n        return false;\n    pos = $from.before(same);\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, pos)));\n    return true;\n};\n/**\nSelect the whole document.\n*/\nconst selectAll = (state, dispatch) => {\n    if (dispatch)\n        dispatch(state.tr.setSelection(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection(state.doc)));\n    return true;\n};\nfunction joinMaybeClear(state, $pos, dispatch) {\n    let before = $pos.nodeBefore, after = $pos.nodeAfter, index = $pos.index();\n    if (!before || !after || !before.type.compatibleContent(after.type))\n        return false;\n    if (!before.content.size && $pos.parent.canReplace(index - 1, index)) {\n        if (dispatch)\n            dispatch(state.tr.delete($pos.pos - before.nodeSize, $pos.pos).scrollIntoView());\n        return true;\n    }\n    if (!$pos.parent.canReplace(index, index + 1) || !(after.isTextblock || (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, $pos.pos)))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.join($pos.pos).scrollIntoView());\n    return true;\n}\nfunction deleteBarrier(state, $cut, dispatch, dir) {\n    let before = $cut.nodeBefore, after = $cut.nodeAfter, conn, match;\n    let isolated = before.type.spec.isolating || after.type.spec.isolating;\n    if (!isolated && joinMaybeClear(state, $cut, dispatch))\n        return true;\n    let canDelAfter = !isolated && $cut.parent.canReplace($cut.index(), $cut.index() + 1);\n    if (canDelAfter &&\n        (conn = (match = before.contentMatchAt(before.childCount)).findWrapping(after.type)) &&\n        match.matchType(conn[0] || after.type).validEnd) {\n        if (dispatch) {\n            let end = $cut.pos + after.nodeSize, wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.empty;\n            for (let i = conn.length - 1; i >= 0; i--)\n                wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(conn[i].create(null, wrap));\n            wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(before.copy(wrap));\n            let tr = state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceAroundStep($cut.pos - 1, end, $cut.pos, end, new prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice(wrap, 1, 0), conn.length, true));\n            let $joinAt = tr.doc.resolve(end + 2 * conn.length);\n            if ($joinAt.nodeAfter && $joinAt.nodeAfter.type == before.type &&\n                (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(tr.doc, $joinAt.pos))\n                tr.join($joinAt.pos);\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    }\n    let selAfter = after.type.spec.isolating || (dir > 0 && isolated) ? null : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom($cut, 1);\n    let range = selAfter && selAfter.$from.blockRange(selAfter.$to), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target != null && target >= $cut.depth) {\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    if (canDelAfter && textblockAt(after, \"start\", true) && textblockAt(before, \"end\")) {\n        let at = before, wrap = [];\n        for (;;) {\n            wrap.push(at);\n            if (at.isTextblock)\n                break;\n            at = at.lastChild;\n        }\n        let afterText = after, afterDepth = 1;\n        for (; !afterText.isTextblock; afterText = afterText.firstChild)\n            afterDepth++;\n        if (at.canReplace(at.childCount, at.childCount, afterText.content)) {\n            if (dispatch) {\n                let end = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.empty;\n                for (let i = wrap.length - 1; i >= 0; i--)\n                    end = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(wrap[i].copy(end));\n                let tr = state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceAroundStep($cut.pos - wrap.length, $cut.pos + after.nodeSize, $cut.pos + afterDepth, $cut.pos + after.nodeSize - afterDepth, new prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice(end, wrap.length, 0), 0, true));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    return false;\n}\nfunction selectTextblockSide(side) {\n    return function (state, dispatch) {\n        let sel = state.selection, $pos = side < 0 ? sel.$from : sel.$to;\n        let depth = $pos.depth;\n        while ($pos.node(depth).isInline) {\n            if (!depth)\n                return false;\n            depth--;\n        }\n        if (!$pos.node(depth).isTextblock)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(state.doc, side < 0 ? $pos.start(depth) : $pos.end(depth))));\n        return true;\n    };\n}\n/**\nMoves the cursor to the start of current text block.\n*/\nconst selectTextblockStart = selectTextblockSide(-1);\n/**\nMoves the cursor to the end of current text block.\n*/\nconst selectTextblockEnd = selectTextblockSide(1);\n// Parameterized commands\n/**\nWrap the selection in a node of the given type with the given\nattributes.\n*/\nfunction wrapIn(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to), wrapping = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.findWrapping)(range, nodeType, attrs);\n        if (!wrapping)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.wrap(range, wrapping).scrollIntoView());\n        return true;\n    };\n}\n/**\nReturns a command that tries to set the selected textblocks to the\ngiven node type with the given attributes.\n*/\nfunction setBlockType(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let applicable = false;\n        for (let i = 0; i < state.selection.ranges.length && !applicable; i++) {\n            let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n            state.doc.nodesBetween(from, to, (node, pos) => {\n                if (applicable)\n                    return false;\n                if (!node.isTextblock || node.hasMarkup(nodeType, attrs))\n                    return;\n                if (node.type == nodeType) {\n                    applicable = true;\n                }\n                else {\n                    let $pos = state.doc.resolve(pos), index = $pos.index();\n                    applicable = $pos.parent.canReplaceWith(index, index + 1, nodeType);\n                }\n            });\n        }\n        if (!applicable)\n            return false;\n        if (dispatch) {\n            let tr = state.tr;\n            for (let i = 0; i < state.selection.ranges.length; i++) {\n                let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n                tr.setBlockType(from, to, nodeType, attrs);\n            }\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    };\n}\nfunction markApplies(doc, ranges, type, enterAtoms) {\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        let can = $from.depth == 0 ? doc.inlineContent && doc.type.allowsMarkType(type) : false;\n        doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (can || !enterAtoms && node.isAtom && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos)\n                return false;\n            can = node.inlineContent && node.type.allowsMarkType(type);\n        });\n        if (can)\n            return true;\n    }\n    return false;\n}\nfunction removeInlineAtoms(ranges) {\n    let result = [];\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        $from.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (node.isAtom && node.content.size && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos) {\n                if (pos + 1 > $from.pos)\n                    result.push(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.SelectionRange($from, $from.doc.resolve(pos + 1)));\n                $from = $from.doc.resolve(pos + 1 + node.content.size);\n                return false;\n            }\n        });\n        if ($from.pos < $to.pos)\n            result.push(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.SelectionRange($from, $to));\n    }\n    return result;\n}\n/**\nCreate a command function that toggles the given mark with the\ngiven attributes. Will return `false` when the current selection\ndoesn't support that mark. This will remove the mark if any marks\nof that type exist in the selection, or add it otherwise. If the\nselection is empty, this applies to the [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks) instead of a range of the\ndocument.\n*/\nfunction toggleMark(markType, attrs = null, options) {\n    let removeWhenPresent = (options && options.removeWhenPresent) !== false;\n    let enterAtoms = (options && options.enterInlineAtoms) !== false;\n    let dropSpace = !(options && options.includeWhitespace);\n    return function (state, dispatch) {\n        let { empty, $cursor, ranges } = state.selection;\n        if ((empty && !$cursor) || !markApplies(state.doc, ranges, markType, enterAtoms))\n            return false;\n        if (dispatch) {\n            if ($cursor) {\n                if (markType.isInSet(state.storedMarks || $cursor.marks()))\n                    dispatch(state.tr.removeStoredMark(markType));\n                else\n                    dispatch(state.tr.addStoredMark(markType.create(attrs)));\n            }\n            else {\n                let add, tr = state.tr;\n                if (!enterAtoms)\n                    ranges = removeInlineAtoms(ranges);\n                if (removeWhenPresent) {\n                    add = !ranges.some(r => state.doc.rangeHasMark(r.$from.pos, r.$to.pos, markType));\n                }\n                else {\n                    add = !ranges.every(r => {\n                        let missing = false;\n                        tr.doc.nodesBetween(r.$from.pos, r.$to.pos, (node, pos, parent) => {\n                            if (missing)\n                                return false;\n                            missing = !markType.isInSet(node.marks) && !!parent && parent.type.allowsMarkType(markType) &&\n                                !(node.isText && /^\\s*$/.test(node.textBetween(Math.max(0, r.$from.pos - pos), Math.min(node.nodeSize, r.$to.pos - pos))));\n                        });\n                        return !missing;\n                    });\n                }\n                for (let i = 0; i < ranges.length; i++) {\n                    let { $from, $to } = ranges[i];\n                    if (!add) {\n                        tr.removeMark($from.pos, $to.pos, markType);\n                    }\n                    else {\n                        let from = $from.pos, to = $to.pos, start = $from.nodeAfter, end = $to.nodeBefore;\n                        let spaceStart = dropSpace && start && start.isText ? /^\\s*/.exec(start.text)[0].length : 0;\n                        let spaceEnd = dropSpace && end && end.isText ? /\\s*$/.exec(end.text)[0].length : 0;\n                        if (from + spaceStart < to) {\n                            from += spaceStart;\n                            to -= spaceEnd;\n                        }\n                        tr.addMark(from, to, markType.create(attrs));\n                    }\n                }\n                dispatch(tr.scrollIntoView());\n            }\n        }\n        return true;\n    };\n}\nfunction wrapDispatchForJoin(dispatch, isJoinable) {\n    return (tr) => {\n        if (!tr.isGeneric)\n            return dispatch(tr);\n        let ranges = [];\n        for (let i = 0; i < tr.mapping.maps.length; i++) {\n            let map = tr.mapping.maps[i];\n            for (let j = 0; j < ranges.length; j++)\n                ranges[j] = map.map(ranges[j]);\n            map.forEach((_s, _e, from, to) => ranges.push(from, to));\n        }\n        // Figure out which joinable points exist inside those ranges,\n        // by checking all node boundaries in their parent nodes.\n        let joinable = [];\n        for (let i = 0; i < ranges.length; i += 2) {\n            let from = ranges[i], to = ranges[i + 1];\n            let $from = tr.doc.resolve(from), depth = $from.sharedDepth(to), parent = $from.node(depth);\n            for (let index = $from.indexAfter(depth), pos = $from.after(depth + 1); pos <= to; ++index) {\n                let after = parent.maybeChild(index);\n                if (!after)\n                    break;\n                if (index && joinable.indexOf(pos) == -1) {\n                    let before = parent.child(index - 1);\n                    if (before.type == after.type && isJoinable(before, after))\n                        joinable.push(pos);\n                }\n                pos += after.nodeSize;\n            }\n        }\n        // Join the joinable points\n        joinable.sort((a, b) => a - b);\n        for (let i = joinable.length - 1; i >= 0; i--) {\n            if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(tr.doc, joinable[i]))\n                tr.join(joinable[i]);\n        }\n        dispatch(tr);\n    };\n}\n/**\nWrap a command so that, when it produces a transform that causes\ntwo joinable nodes to end up next to each other, those are joined.\nNodes are considered joinable when they are of the same type and\nwhen the `isJoinable` predicate returns true for them or, if an\narray of strings was passed, if their node type name is in that\narray.\n*/\nfunction autoJoin(command, isJoinable) {\n    let canJoin = Array.isArray(isJoinable) ? (node) => isJoinable.indexOf(node.type.name) > -1\n        : isJoinable;\n    return (state, dispatch, view) => command(state, dispatch && wrapDispatchForJoin(dispatch, canJoin), view);\n}\n/**\nCombine a number of command functions into a single function (which\ncalls them one by one until one returns true).\n*/\nfunction chainCommands(...commands) {\n    return function (state, dispatch, view) {\n        for (let i = 0; i < commands.length; i++)\n            if (commands[i](state, dispatch, view))\n                return true;\n        return false;\n    };\n}\nlet backspace = chainCommands(deleteSelection, joinBackward, selectNodeBackward);\nlet del = chainCommands(deleteSelection, joinForward, selectNodeForward);\n/**\nA basic keymap containing bindings not specific to any schema.\nBinds the following keys (when multiple commands are listed, they\nare chained with [`chainCommands`](https://prosemirror.net/docs/ref/#commands.chainCommands)):\n\n* **Enter** to `newlineInCode`, `createParagraphNear`, `liftEmptyBlock`, `splitBlock`\n* **Mod-Enter** to `exitCode`\n* **Backspace** and **Mod-Backspace** to `deleteSelection`, `joinBackward`, `selectNodeBackward`\n* **Delete** and **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-a** to `selectAll`\n*/\nconst pcBaseKeymap = {\n    \"Enter\": chainCommands(newlineInCode, createParagraphNear, liftEmptyBlock, splitBlock),\n    \"Mod-Enter\": exitCode,\n    \"Backspace\": backspace,\n    \"Mod-Backspace\": backspace,\n    \"Shift-Backspace\": backspace,\n    \"Delete\": del,\n    \"Mod-Delete\": del,\n    \"Mod-a\": selectAll\n};\n/**\nA copy of `pcBaseKeymap` that also binds **Ctrl-h** like Backspace,\n**Ctrl-d** like Delete, **Alt-Backspace** like Ctrl-Backspace, and\n**Ctrl-Alt-Backspace**, **Alt-Delete**, and **Alt-d** like\nCtrl-Delete.\n*/\nconst macBaseKeymap = {\n    \"Ctrl-h\": pcBaseKeymap[\"Backspace\"],\n    \"Alt-Backspace\": pcBaseKeymap[\"Mod-Backspace\"],\n    \"Ctrl-d\": pcBaseKeymap[\"Delete\"],\n    \"Ctrl-Alt-Backspace\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-Delete\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-d\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Ctrl-a\": selectTextblockStart,\n    \"Ctrl-e\": selectTextblockEnd\n};\nfor (let key in pcBaseKeymap)\n    macBaseKeymap[key] = pcBaseKeymap[key];\nconst mac = typeof navigator != \"undefined\" ? /Mac|iP(hone|[oa]d)/.test(navigator.platform)\n    // @ts-ignore\n    : typeof os != \"undefined\" && os.platform ? os.platform() == \"darwin\" : false;\n/**\nDepending on the detected platform, this will hold\n[`pcBasekeymap`](https://prosemirror.net/docs/ref/#commands.pcBaseKeymap) or\n[`macBaseKeymap`](https://prosemirror.net/docs/ref/#commands.macBaseKeymap).\n*/\nconst baseKeymap = mac ? macBaseKeymap : pcBaseKeymap;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-commands/dist/index.js\n");

/***/ })

};
;