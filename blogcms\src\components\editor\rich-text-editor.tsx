'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import Youtube from '@tiptap/extension-youtube'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import Color from '@tiptap/extension-color'
import Highlight from '@tiptap/extension-highlight'
import FontFamily from '@tiptap/extension-font-family'
import Underline from '@tiptap/extension-underline'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import TaskList from '@tiptap/extension-task-list'
import TaskItem from '@tiptap/extension-task-item'
import CharacterCount from '@tiptap/extension-character-count'
import Placeholder from '@tiptap/extension-placeholder'
import { createLowlight } from 'lowlight'
import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { EditorToolbar } from './editor-toolbar'
import { MediaUploadDialog } from './media-upload-dialog'
// import { ChartDialog } from './chart-dialog'
import { TableDialog } from './table-dialog'

interface RichTextEditorProps {
  content?: string
  onChange?: (content: string) => void
  placeholder?: string
  className?: string
  readOnly?: boolean
}

export function RichTextEditor({
  content = '',
  onChange,
  placeholder = 'Start writing your story...',
  className = '',
  readOnly = false
}: RichTextEditorProps) {
  const [isMediaDialogOpen, setIsMediaDialogOpen] = useState(false)
  const [isChartDialogOpen, setIsChartDialogOpen] = useState(false)
  const [isTableDialogOpen, setIsTableDialogOpen] = useState(false)

  const lowlight = createLowlight()

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // We'll use CodeBlockLowlight instead
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto',
        },
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'border-collapse border border-gray-300 my-4',
        },
      }),
      TableRow.configure({
        HTMLAttributes: {
          class: 'border border-gray-300',
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: 'border border-gray-300 bg-gray-50 font-semibold p-2',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-gray-300 p-2',
        },
      }),
      Youtube.configure({
        width: 640,
        height: 480,
        HTMLAttributes: {
          class: 'rounded-lg my-4',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Underline,
      Subscript,
      Superscript,
      CodeBlockLowlight.configure({
        lowlight,
        HTMLAttributes: {
          class: 'bg-gray-100 rounded-lg p-4 my-4 overflow-x-auto',
        },
      }),
      TaskList.configure({
        HTMLAttributes: {
          class: 'not-prose',
        },
      }),
      TaskItem.configure({
        nested: true,
        HTMLAttributes: {
          class: 'flex items-start gap-2',
        },
      }),
      CharacterCount,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getHTML())
    },
    editorProps: {
      attributes: {
        class: `prose prose-lg max-w-none focus:outline-none min-h-[400px] p-4 ${className}`,
      },
    },
  })

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      acceptedFiles.forEach((file) => {
        if (file.type.startsWith('image/')) {
          const reader = new FileReader()
          reader.onload = () => {
            const url = reader.result as string
            editor?.chain().focus().setImage({ src: url }).run()
          }
          reader.readAsDataURL(file)
        }
      })
    },
    [editor]
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
      'video/*': ['.mp4', '.webm', '.ogg'],
      'application/pdf': ['.pdf'],
    },
    noClick: true,
  })

  if (!editor) {
    return (
      <div className="animate-pulse">
        <div className="h-12 bg-gray-200 rounded mb-4"></div>
        <div className="h-64 bg-gray-200 rounded"></div>
      </div>
    )
  }

  return (
    <div className="border rounded-lg overflow-hidden">
      {!readOnly && (
        <EditorToolbar
          editor={editor}
          onOpenMediaDialog={() => setIsMediaDialogOpen(true)}
          onOpenTableDialog={() => setIsTableDialogOpen(true)}
        />
      )}
      
      <div
        {...getRootProps()}
        className={`relative ${isDragActive ? 'bg-blue-50 border-blue-300' : ''}`}
      >
        <input {...getInputProps()} />
        
        {isDragActive && (
          <div className="absolute inset-0 bg-blue-50 bg-opacity-90 flex items-center justify-center z-10 border-2 border-dashed border-blue-300">
            <p className="text-blue-600 font-medium">Drop files here to upload</p>
          </div>
        )}
        
        <EditorContent editor={editor} />
      </div>

      {!readOnly && editor.storage.characterCount && (
        <div className="px-4 py-2 bg-gray-50 border-t text-sm text-gray-600 flex justify-between">
          <span>
            {editor.storage.characterCount.characters()} characters, {editor.storage.characterCount.words()} words
          </span>
          <span>
            Reading time: ~{Math.ceil(editor.storage.characterCount.words() / 200)} min
          </span>
        </div>
      )}

      <MediaUploadDialog
        isOpen={isMediaDialogOpen}
        onClose={() => setIsMediaDialogOpen(false)}
        onInsert={(url, type) => {
          if (type === 'image') {
            editor.chain().focus().setImage({ src: url }).run()
          } else if (type === 'video') {
            editor.chain().focus().setYoutubeVideo({ src: url }).run()
          }
        }}
      />

      {/* <ChartDialog
        isOpen={isChartDialogOpen}
        onClose={() => setIsChartDialogOpen(false)}
        onInsert={(chartHtml) => {
          editor.chain().focus().insertContent(chartHtml).run()
        }}
      /> */}

      <TableDialog
        isOpen={isTableDialogOpen}
        onClose={() => setIsTableDialogOpen(false)}
        onInsert={(rows, cols) => {
          editor.chain().focus().insertTable({ rows, cols, withHeaderRow: true }).run()
        }}
      />
    </div>
  )
}
