"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lowlight";
exports.ids = ["vendor-chunks/lowlight"];
exports.modules = {

/***/ "(ssr)/./node_modules/lowlight/lib/index.js":
/*!********************************************!*\
  !*** ./node_modules/lowlight/lib/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLowlight: () => (/* binding */ createLowlight)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var highlight_js_lib_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! highlight.js/lib/core */ \"(ssr)/./node_modules/highlight.js/es/core.js\");\n/**\n * @import {ElementContent, Element, RootData, Root} from 'hast'\n * @import {Emitter, HLJSOptions as HljsOptions, HighlightResult, LanguageFn} from 'highlight.js'\n */\n\n/**\n * @typedef {Object} ExtraOptions\n *   Extra fields.\n * @property {ReadonlyArray<string> | null | undefined} [subset]\n *   List of allowed languages (default: all registered languages).\n *\n * @typedef Options\n *   Configuration for `highlight`.\n * @property {string | null | undefined} [prefix='hljs-']\n *   Class prefix (default: `'hljs-'`).\n *\n * @typedef {Options & ExtraOptions} AutoOptions\n *   Configuration for `highlightAuto`.\n */\n\n\n\n\n/** @type {AutoOptions} */\nconst emptyOptions = {}\n\nconst defaultPrefix = 'hljs-'\n\n/**\n * Create a `lowlight` instance.\n *\n * @param {Readonly<Record<string, LanguageFn>> | null | undefined} [grammars]\n *   Grammars to add (optional).\n * @returns\n *   Lowlight.\n */\nfunction createLowlight(grammars) {\n  const high = highlight_js_lib_core__WEBPACK_IMPORTED_MODULE_0__[\"default\"].newInstance()\n\n  if (grammars) {\n    register(grammars)\n  }\n\n  return {\n    highlight,\n    highlightAuto,\n    listLanguages,\n    register,\n    registerAlias,\n    registered\n  }\n\n  /**\n   * Highlight `value` (code) as `language` (name).\n   *\n   * @example\n   *   ```js\n   *   import {common, createLowlight} from 'lowlight'\n   *\n   *   const lowlight = createLowlight(common)\n   *\n   *   console.log(lowlight.highlight('css', 'em { color: red }'))\n   *   ```\n   *\n   *   Yields:\n   *\n   *   ```js\n   *   {type: 'root', children: [Array], data: {language: 'css', relevance: 3}}\n   *   ```\n   *\n   * @param {string} language\n   *   Programming language name.\n   * @param {string} value\n   *   Code to highlight.\n   * @param {Readonly<Options> | null | undefined} [options={}]\n   *   Configuration (optional).\n   * @returns {Root}\n   *   Tree; with the following `data` fields: `language` (`string`), detected\n   *   programming language name; `relevance` (`number`), how sure lowlight is\n   *   that the given code is in the language.\n   */\n  function highlight(language, value, options) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof language === 'string', 'expected `string` as `name`')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof value === 'string', 'expected `string` as `value`')\n    const settings = options || emptyOptions\n    const prefix =\n      typeof settings.prefix === 'string' ? settings.prefix : defaultPrefix\n\n    if (!high.getLanguage(language)) {\n      throw new Error('Unknown language: `' + language + '` is not registered')\n    }\n\n    // See: <https://github.com/highlightjs/highlight.js/issues/3621#issuecomment-1528841888>\n    high.configure({__emitter: HastEmitter, classPrefix: prefix})\n\n    const result = /** @type {HighlightResult & {_emitter: HastEmitter}} */ (\n      high.highlight(value, {ignoreIllegals: true, language})\n    )\n\n    // `highlight.js` seems to use this (currently) for broken grammars, so let’s\n    // keep it in there just to be sure.\n    /* c8 ignore next 5 */\n    if (result.errorRaised) {\n      throw new Error('Could not highlight with `Highlight.js`', {\n        cause: result.errorRaised\n      })\n    }\n\n    const root = result._emitter.root\n\n    // Cast because it is always defined.\n    const data = /** @type {RootData} */ (root.data)\n\n    data.language = result.language\n    data.relevance = result.relevance\n\n    return root\n  }\n\n  /**\n   * Highlight `value` (code) and guess its programming language.\n   *\n   * @example\n   *   ```js\n   *   import {common, createLowlight} from 'lowlight'\n   *\n   *   const lowlight = createLowlight(common)\n   *\n   *   console.log(lowlight.highlightAuto('\"hello, \" + name + \"!\"'))\n   *   ```\n   *\n   *   Yields:\n   *\n   *   ```js\n   *   {type: 'root', children: [Array], data: {language: 'arduino', relevance: 2}}\n   *   ```\n   *\n   * @param {string} value\n   *   Code to highlight.\n   * @param {Readonly<AutoOptions> | null | undefined} [options={}]\n   *   Configuration (optional).\n   * @returns {Root}\n   *   Tree; with the following `data` fields: `language` (`string`), detected\n   *   programming language name; `relevance` (`number`), how sure lowlight is\n   *   that the given code is in the language.\n   */\n  function highlightAuto(value, options) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof value === 'string', 'expected `string` as `value`')\n    const settings = options || emptyOptions\n    const subset = settings.subset || listLanguages()\n\n    let index = -1\n    let relevance = 0\n    /** @type {Root | undefined} */\n    let result\n\n    while (++index < subset.length) {\n      const name = subset[index]\n\n      if (!high.getLanguage(name)) continue\n\n      const current = highlight(name, value, options)\n\n      if (\n        current.data &&\n        current.data.relevance !== undefined &&\n        current.data.relevance > relevance\n      ) {\n        relevance = current.data.relevance\n        result = current\n      }\n    }\n\n    return (\n      result || {\n        type: 'root',\n        children: [],\n        data: {language: undefined, relevance}\n      }\n    )\n  }\n\n  /**\n   * List registered languages.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import markdown from 'highlight.js/lib/languages/markdown'\n   *\n   *   const lowlight = createLowlight()\n   *\n   *   console.log(lowlight.listLanguages()) // => []\n   *\n   *   lowlight.register({markdown})\n   *\n   *   console.log(lowlight.listLanguages()) // => ['markdown']\n   *   ```\n   *\n   * @returns {Array<string>}\n   *   Names of registered language.\n   */\n  function listLanguages() {\n    return high.listLanguages()\n  }\n\n  /**\n   * Register languages.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import xml from 'highlight.js/lib/languages/xml'\n   *\n   *   const lowlight = createLowlight()\n   *\n   *   lowlight.register({xml})\n   *\n   *   // Note: `html` is an alias for `xml`.\n   *   console.log(lowlight.highlight('html', '<em>Emphasis</em>'))\n   *   ```\n   *\n   *   Yields:\n   *\n   *   ```js\n   *   {type: 'root', children: [Array], data: {language: 'html', relevance: 2}}\n   *   ```\n   *\n   * @overload\n   * @param {Readonly<Record<string, LanguageFn>>} grammars\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {string} name\n   * @param {LanguageFn} grammar\n   * @returns {undefined}\n   *\n   * @param {Readonly<Record<string, LanguageFn>> | string} grammarsOrName\n   *   Grammars or programming language name.\n   * @param {LanguageFn | undefined} [grammar]\n   *   Grammar, if with name.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function register(grammarsOrName, grammar) {\n    if (typeof grammarsOrName === 'string') {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(grammar !== undefined, 'expected `grammar`')\n      high.registerLanguage(grammarsOrName, grammar)\n    } else {\n      /** @type {string} */\n      let name\n\n      for (name in grammarsOrName) {\n        if (Object.hasOwn(grammarsOrName, name)) {\n          high.registerLanguage(name, grammarsOrName[name])\n        }\n      }\n    }\n  }\n\n  /**\n   * Register aliases.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import markdown from 'highlight.js/lib/languages/markdown'\n   *\n   *   const lowlight = createLowlight()\n   *\n   *   lowlight.register({markdown})\n   *\n   *   // lowlight.highlight('mdown', '<em>Emphasis</em>')\n   *   // ^ would throw: Error: Unknown language: `mdown` is not registered\n   *\n   *   lowlight.registerAlias({markdown: ['mdown', 'mkdn', 'mdwn', 'ron']})\n   *   lowlight.highlight('mdown', '<em>Emphasis</em>')\n   *   // ^ Works!\n   *   ```\n   *\n   * @overload\n   * @param {Readonly<Record<string, ReadonlyArray<string> | string>>} aliases\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {string} language\n   * @param {ReadonlyArray<string> | string} alias\n   * @returns {undefined}\n   *\n   * @param {Readonly<Record<string, ReadonlyArray<string> | string>> | string} aliasesOrName\n   *   Map of programming language names to one or more aliases, or programming\n   *   language name.\n   * @param {ReadonlyArray<string> | string | undefined} [alias]\n   *   One or more aliases for the programming language, if with `name`.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function registerAlias(aliasesOrName, alias) {\n    if (typeof aliasesOrName === 'string') {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(alias !== undefined)\n      high.registerAliases(\n        // Note: copy needed because hljs doesn’t accept readonly arrays yet.\n        typeof alias === 'string' ? alias : [...alias],\n        {languageName: aliasesOrName}\n      )\n    } else {\n      /** @type {string} */\n      let key\n\n      for (key in aliasesOrName) {\n        if (Object.hasOwn(aliasesOrName, key)) {\n          const aliases = aliasesOrName[key]\n          high.registerAliases(\n            // Note: copy needed because hljs doesn’t accept readonly arrays yet.\n            typeof aliases === 'string' ? aliases : [...aliases],\n            {languageName: key}\n          )\n        }\n      }\n    }\n  }\n\n  /**\n   * Check whether an alias or name is registered.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import javascript from 'highlight.js/lib/languages/javascript'\n   *\n   *   const lowlight = createLowlight({javascript})\n   *\n   *   console.log(lowlight.registered('funkyscript')) // => `false`\n   *\n   *   lowlight.registerAlias({javascript: 'funkyscript'})\n   *   console.log(lowlight.registered('funkyscript')) // => `true`\n   *   ```\n   *\n   * @param {string} aliasOrName\n   *   Name of a language or alias for one.\n   * @returns {boolean}\n   *   Whether `aliasOrName` is registered.\n   */\n  function registered(aliasOrName) {\n    return Boolean(high.getLanguage(aliasOrName))\n  }\n}\n\n/** @type {Emitter} */\nclass HastEmitter {\n  /**\n   * @param {Readonly<HljsOptions>} options\n   *   Configuration.\n   * @returns\n   *   Instance.\n   */\n  constructor(options) {\n    /** @type {HljsOptions} */\n    this.options = options\n    /** @type {Root} */\n    this.root = {\n      type: 'root',\n      children: [],\n      data: {language: undefined, relevance: 0}\n    }\n    /** @type {[Root, ...Array<Element>]} */\n    this.stack = [this.root]\n  }\n\n  /**\n   * @param {string} value\n   *   Text to add.\n   * @returns {undefined}\n   *   Nothing.\n   *\n   */\n  addText(value) {\n    if (value === '') return\n\n    const current = this.stack[this.stack.length - 1]\n    const tail = current.children[current.children.length - 1]\n\n    if (tail && tail.type === 'text') {\n      tail.value += value\n    } else {\n      current.children.push({type: 'text', value})\n    }\n  }\n\n  /**\n   *\n   * @param {unknown} rawName\n   *   Name to add.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  startScope(rawName) {\n    this.openNode(String(rawName))\n  }\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  endScope() {\n    this.closeNode()\n  }\n\n  /**\n   * @param {HastEmitter} other\n   *   Other emitter.\n   * @param {string} name\n   *   Name of the sublanguage.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  __addSublanguage(other, name) {\n    const current = this.stack[this.stack.length - 1]\n    // Assume only element content.\n    const results = /** @type {Array<ElementContent>} */ (other.root.children)\n\n    if (name) {\n      current.children.push({\n        type: 'element',\n        tagName: 'span',\n        properties: {className: [name]},\n        children: results\n      })\n    } else {\n      current.children.push(...results)\n    }\n  }\n\n  /**\n   * @param {string} name\n   *   Name to add.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  openNode(name) {\n    const self = this\n    // First “class” gets the prefix. Rest gets a repeated underscore suffix.\n    // See: <https://github.com/highlightjs/highlight.js/commit/51806aa>\n    // See: <https://github.com/wooorm/lowlight/issues/43>\n    const className = name.split('.').map(function (d, i) {\n      return i ? d + '_'.repeat(i) : self.options.classPrefix + d\n    })\n    const current = this.stack[this.stack.length - 1]\n    /** @type {Element} */\n    const child = {\n      type: 'element',\n      tagName: 'span',\n      properties: {className},\n      children: []\n    }\n\n    current.children.push(child)\n    this.stack.push(child)\n  }\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  closeNode() {\n    this.stack.pop()\n  }\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  finalize() {}\n\n  /**\n   * @returns {string}\n   *   Nothing.\n   */\n  toHTML() {\n    return ''\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lowlight/lib/index.js\n");

/***/ })

};
;